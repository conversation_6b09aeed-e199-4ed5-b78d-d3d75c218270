name: Docker Build

on:
  workflow_call:
    inputs:
      push:
        description: 'Push images to registry'
        required: false
        type: boolean
        default: false
      registry:
        description: 'Docker registry'
        required: false
        type: string
        default: ''
      tag-prefix:
        description: 'Tag prefix for images'
        required: false
        type: string
        default: 'latest'
      report-sizes:
        description: 'Report Docker image sizes in summary'
        required: false
        type: boolean
        default: false
    secrets:
      DOCKER_USERNAME:
        required: false
      DOCKER_PASSWORD:
        required: false

jobs:
  docker-build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        service: [agent_service, streamlit_app]

    steps:
    - uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      if: ${{ inputs.push && inputs.registry }}
      uses: docker/login-action@v3
      with:
        registry: ${{ inputs.registry }}
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}

    - name: Extract metadata
      if: ${{ inputs.push && inputs.registry }}
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ inputs.registry }}/codepluse/${{ matrix.service }}
        tags: |
          type=raw,value=${{ inputs.tag-prefix }}

    - name: Setup dockerignore for build
      run: |
        if [ "${{ matrix.service }}" = "agent_service" ]; then
          cp .dockerignore.service .dockerignore
        else
          cp .dockerignore.app .dockerignore
        fi

    - name: Build and push Docker image
      uses: docker/build-push-action@v6
      with:
        context: .
        file: docker/Dockerfile.${{ matrix.service == 'agent_service' && 'service' || 'app' }}
        push: ${{ inputs.push }}
        tags: ${{ inputs.push && inputs.registry && steps.meta.outputs.tags || format('{0}:latest', matrix.service) }}
        labels: ${{ inputs.push && inputs.registry && steps.meta.outputs.labels || '' }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Report image size and optimization info
      if: ${{ inputs.report-sizes }}
      run: |
        IMAGE_TAG="${{ inputs.push && inputs.registry && steps.meta.outputs.tags || format('{0}:latest', matrix.service) }}"
        IMAGE_SIZE=$(docker images $IMAGE_TAG --format 'table {{.Size}}' | tail -n 1)
        IMAGE_SIZE_BYTES=$(docker images $IMAGE_TAG --format 'table {{.Size}}' | tail -n 1 | sed 's/[^0-9.]//g')

        # Get image details for optimization info
        IMAGE_LAYERS=$(docker history $IMAGE_TAG --no-trunc --format "table {{.Size}}" | grep -v SIZE | wc -l)

        # Create or append to size summary with optimization details
        if [ "${{ matrix.service }}" = "agent_service" ]; then
          echo "## 📦 Docker Image Sizes & Optimization" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| Service | Size | Layers | Optimization |" >> $GITHUB_STEP_SUMMARY
          echo "|---------|------|--------|-------------|" >> $GITHUB_STEP_SUMMARY
          echo "| Agent Service | $IMAGE_SIZE | $IMAGE_LAYERS | Multi-stage Alpine build |" >> $GITHUB_STEP_SUMMARY
        else
          echo "| Streamlit App | $IMAGE_SIZE | $IMAGE_LAYERS | Optimized Alpine + minimal deps |" >> $GITHUB_STEP_SUMMARY
        fi

    - name: Add Docker build summary
      run: |
        if [ "${{ inputs.push }}" = "true" ] && [ -n "${{ inputs.registry }}" ]; then
          FULL_IMAGE_NAME="${{ inputs.registry }}/codepluse/${{ matrix.service }}:${{ inputs.tag-prefix }}"
          echo "## 🐳 Docker Build Summary - ${{ matrix.service }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Full Image Name:** \`$FULL_IMAGE_NAME\`" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "- **Registry:** ${{ inputs.registry }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Service:** ${{ matrix.service }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Tag:** ${{ inputs.tag-prefix }}" >> $GITHUB_STEP_SUMMARY
          if [ "${{ matrix.service }}" = "streamlit_app" ]; then
            echo "- **Optimization:** Multi-stage Alpine build with minimal dependencies" >> $GITHUB_STEP_SUMMARY
          fi
          echo "" >> $GITHUB_STEP_SUMMARY
        else
          echo "## 🐳 Docker Build Summary - ${{ matrix.service }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Local Image:** \`${{ matrix.service }}:latest\`" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "- **Service:** ${{ matrix.service }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Tag:** latest" >> $GITHUB_STEP_SUMMARY
          echo "- **Status:** Built locally (not pushed)" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
        fi

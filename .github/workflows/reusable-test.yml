name: Unit Tests

on:
  workflow_call:
    inputs:
      python-version:
        description: 'Python version to use'
        required: false
        type: string
        default: '3.12'
      uv-version:
        description: 'UV version to use'
        required: false
        type: string
        default: '0.5.11'
      upload-coverage:
        description: 'Upload coverage to Codecov'
        required: false
        type: boolean
        default: true
    secrets:
      OPENWEATHERMAP_API_KEY:
        required: true
      OPENROUTER_API_KEY:
        required: true
      OPENROUTER_MODEL:
        required: true
      OPENROUTER_BASEURL:
        required: true
      CODECOV_TOKEN:
        required: false

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: ${{ inputs.python-version }}

    - name: Install uv
      uses: astral-sh/setup-uv@v6
      with:
        version: ${{ inputs.uv-version }}

    - name: Install dependencies
      run: |
        uv sync --frozen --group dev

    - name: Create test environment file
      run: |
        cat > .env << EOF
        OPENAI_API_KEY=sk-fake-openai-key
        POSTGRES_USER=postgres
        POSTGRES_PASSWORD=postgres
        POSTGRES_DB=agent_service
        OPENWEATHERMAP_API_KEY=${{ secrets.OPENWEATHERMAP_API_KEY }}
        OPENROUTER_API_KEY=${{ secrets.OPENROUTER_API_KEY }}
        OPENROUTER_MODEL=${{ secrets.OPENROUTER_MODEL }}
        OPENROUTER_BASEURL=${{ secrets.OPENROUTER_BASEURL }}
        EOF

    - name: Run unit tests
      env:
        POSTGRES_URL: postgresql://postgres:postgres@localhost:5432/agent_service_test
        OPENAI_API_KEY: sk-fake-openai-key
      run: |
        echo "Running unit tests without database dependencies..."
        uv run pytest tests/ -v --cov=src --cov-report=xml --cov-report=term-missing \
          -k "not test_postgres and not test_database and not test_db" \
          --ignore=tests/integration/

    - name: Upload coverage to Codecov
      if: ${{ inputs.upload-coverage }}
      uses: codecov/codecov-action@v5
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        token: ${{ secrets.CODECOV_TOKEN }}

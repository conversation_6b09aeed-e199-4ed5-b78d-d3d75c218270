name: Detect Changes

on:
  workflow_call:
    outputs:
      python:
        description: 'Python files changed'
        value: ${{ jobs.changes.outputs.python }}
      docker:
        description: 'Docker-related files changed'
        value: ${{ jobs.changes.outputs.docker }}

jobs:
  changes:
    runs-on: ubuntu-latest
    outputs:
      python: ${{ steps.changes.outputs.python }}
      docker: ${{ steps.changes.outputs.docker }}
    steps:
    - uses: actions/checkout@v4
    - uses: dorny/paths-filter@v3
      id: changes
      with:
        filters: |
          python:
            - '**/*.py'
          docker:
            - '**/*.py'
            - '**/*.yml'
            - '**/*.yaml'
            - '**/Dockerfile*'

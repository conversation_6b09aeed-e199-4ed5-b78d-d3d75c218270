name: Python Setup

on:
  workflow_call:
    inputs:
      python-version:
        description: 'Python version to use'
        required: false
        type: string
        default: '3.12'
      uv-version:
        description: 'UV version to use'
        required: false
        type: string
        default: '0.5.11'
      install-dev:
        description: 'Install dev dependencies'
        required: false
        type: boolean
        default: true

jobs:
  setup:
    runs-on: ubuntu-latest
    steps:
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: ${{ inputs.python-version }}

    - name: Install uv
      uses: astral-sh/setup-uv@v6
      with:
        version: ${{ inputs.uv-version }}

    - name: Install dependencies
      run: |
        if [ "${{ inputs.install-dev }}" = "true" ]; then
          uv sync --frozen --group dev
        else
          uv sync --frozen
        fi

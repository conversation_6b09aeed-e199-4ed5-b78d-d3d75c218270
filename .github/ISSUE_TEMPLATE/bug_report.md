---
name: Bug report
about: Create a report to help us improve
title: '[BUG] '
labels: 'bug'
assignees: ''

---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Environment (please complete the following information):**
 - OS: [e.g. Ubuntu 22.04, macOS 14.0, Windows 11]
 - Docker version: [e.g. 24.0.7]
 - Python version: [e.g. 3.12.3]
 - Browser [e.g. chrome, safari] (if applicable)

**Service Information:**
 - Agent Service version: [e.g. v1.0.0]
 - Streamlit App version: [e.g. v1.0.0]
 - Deployment method: [e.g. Docker Compose, Kubernetes, local development]

**Logs**
If applicable, add relevant log output:

```
Paste log output here
```

**Additional context**
Add any other context about the problem here.

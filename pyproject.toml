[project]
name = "agent-service-toolkit"
version = "0.1.0"
description = "Full toolkit for running an AI agent service built with LangGraph, FastAPI and Streamlit"
readme = "README.md"
authors = [{ name = "<PERSON>", email = "<EMAIL>" }]
classifiers = [
    "Development Status :: 4 - Beta",
    "License :: OSI Approved :: MIT License",
    "Framework :: FastAPI",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
]

requires-python = ">=3.11"

dependencies = [
    "duckduckgo-search>=7.3.0",
    "fastapi ~=0.115.5",
    "grpcio >=1.68.0",
    "httpx ~=0.27.2",
    "jiter ~=0.8.2",
    "langchain-core ~=0.3.33",
    "langchain-community ~=0.3.16",
    "langchain-anthropic ~= 0.3.0",
    "langchain-aws ~=0.2.14",
    "langchain-chroma ~=0.2.3",
    "langchain-google-genai ~=2.1.4",
    "langchain-google-vertexai>=2.0.7",
    "langchain-groq ~=0.2.1",
    "langchain-ollama ~=0.2.3",
    "langchain-openai ~=0.3.0",
    "langgraph ~=0.3.5",
    "langgraph-checkpoint-mongodb ~=0.1.3",
    "langgraph-checkpoint-postgres ~=2.0.13",
    "langgraph-checkpoint-sqlite ~=2.0.1",
    "langgraph-supervisor ~=0.0.8",
    "langsmith ~=0.3.42",
    "numexpr ~=2.10.1",
    "numpy ~=1.26.4; python_version <= '3.12'",
    "numpy ~=2.2.3; python_version >= '3.13'",
    "onnxruntime ~= 1.21.1",
    "pandas ~=2.2.3",
    "psycopg[binary,pool] ~=3.2.4",
    "pyarrow >=18.1.0",
    "pydantic ~=2.11.5",
    "pydantic-settings ~=2.6.1",
    "pyowm ~=3.3.0",
    "python-dotenv ~=1.1.0",
    "setuptools ~=75.6.0",
    "streamlit ~=1.45.1",
    "tiktoken >=0.8.0",
    "uvicorn ~=0.32.1",
    "pypdf ~=5.3.0",
    "docx2txt ~=0.8",

]

[dependency-groups]
dev = [
    "pre-commit",
    "pytest",
    "pytest-cov",
    "pytest-env",
    "pytest-asyncio",
    "ruff",
    "mypy",
]

# Group for the minimal dependencies to run just the client and Streamlit app.
# These are also installed in the default dependencies.
# To install run: `uv sync --frozen --only-group client`
client = [
    "httpx~=0.27.2",
    "pydantic ~=2.11.5",
    "python-dotenv ~=1.1.0",
    "streamlit~=1.45.1",
]

[tool.ruff]
line-length = 100
target-version = "py311"

[tool.ruff.lint]
extend-select = ["I", "U"]
ignore = ["E501"]  # Line too long (handled by formatter)

[tool.ruff.format]
quote-style = "double"
indent-style = "space"

[tool.pytest.ini_options]
pythonpath = ["src"]
asyncio_default_fixture_loop_scope = "function"

[tool.pytest_env]
OPENAI_API_KEY = "sk-fake-openai-key"
BRAVE_SEARCH_API_KEY = "fake-brave-search-api-key"

[tool.mypy]
plugins = "pydantic.mypy"
exclude = "src/streamlit_app.py"
ignore_missing_imports = true
no_strict_optional = true

[[tool.mypy.overrides]]
module = ["numexpr.*", "langchain.*", "langchain_core.*", "langchain_community.*", "langchain_chroma.*", "chromadb.*", "hnswlib.*"]
ignore_missing_imports = true
follow_untyped_imports = true

version: '3.8'

services:
  codepluse_agent_service:
    image: dockerhub.csharpp.com/codepluse/agent_service:${VERSION:-latest}
    environment:
      # Add your environment variables here
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
      - LANGSMITH_API_KEY=${LANGSMITH_API_KEY:-}
      - LANGSMITH_PROJECT=${LANGSMITH_PROJECT:-}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY:-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}
      - OPENROUTER_MODEL=${OPENROUTER_MODEL:-qwen/qwen3-235b-a22b}
      - OPENROUTER_BASEURL=${OPENROUTER_BASEURL:-https://openrouter.ai/api/v1}
      - OPENWEATHERMAP_API_KEY=${OPENWEATHERMAP_API_KEY:-********************************}
      - BRAVE_SEARCH_API_KEY=${BRAVE_SEARCH_API_KEY:-BSAm3V_RwMeOMpifscQLjSfMj5y034x}
      - DEFAULT_MODEL=${DEFAULT_MODEL:-openrouter}
    networks:
      internal:
      traefik_main:
    extra_hosts:
      - "dockerhub.csharpp.com:***********"
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
        order: start-first
      rollback_config:
        parallelism: 1
        delay: 5s
        failure_action: pause
        order: stop-first
      # resources:
      #   limits:
      #     memory: 2G
      #     cpus: '1.0'
      #   reservations:
      #     memory: 1G
      #     cpus: '0.5'
      labels:
        # Traefik configuration
        - "traefik.enable=true"
        - "traefik.docker.network=traefik_main"

        # HTTP router
        - "traefik.http.routers.codepluse-agent-service.rule=Host(`codepluse-agent.csharpp.com`)"
        - "traefik.http.routers.codepluse-agent-service.entrypoints=https"
        - "traefik.http.routers.codepluse-agent-service.tls=true"
        - "traefik.http.routers.codepluse-agent-service.tls.certresolver=le"

        # Service configuration
        - "traefik.http.services.codepluse-agent-service.loadbalancer.server.port=8080"
        - "traefik.http.services.codepluse-agent-service.loadbalancer.healthcheck.path=/info"
        - "traefik.http.services.codepluse-agent-service.loadbalancer.healthcheck.interval=30s"

        # Middleware for security headers
        - "traefik.http.routers.codepluse-agent-service.middlewares=security-headers"
        - "traefik.http.middlewares.security-headers.headers.customrequestheaders.X-Forwarded-Proto=https"
        - "traefik.http.middlewares.security-headers.headers.customrequestheaders.X-Forwarded-For="
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/info"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  codepluse_streamlit_app:
    image: dockerhub.csharpp.com/codepluse/streamlit_app:${VERSION:-latest}
    environment:
      - AGENT_URL=http://codepluse_agent_service:8080
      # - OPENAI_API_KEY=${OPENAI_API_KEY}
    networks:
      internal:
      traefik_main:
    extra_hosts:
      - "dockerhub.csharpp.com:***********"
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
        order: start-first
      rollback_config:
        parallelism: 1
        delay: 5s
        failure_action: pause
        order: stop-first
      # resources:
      #   limits:
      #     memory: 1G
      #     cpus: '0.5'
      #   reservations:
      #     memory: 512M
      #     cpus: '0.25'
      labels:
        # Traefik configuration
        - "traefik.enable=true"
        - "traefik.docker.network=traefik_main"

        # HTTP router
        - "traefik.http.routers.codepluse-streamlit-app.rule=Host(`codepluse-streamlit.csharpp.com`)"
        - "traefik.http.routers.codepluse-streamlit-app.entrypoints=https"
        - "traefik.http.routers.codepluse-streamlit-app.tls=true"
        - "traefik.http.routers.codepluse-streamlit-app.tls.certresolver=le"

        # Service configuration
        - "traefik.http.services.codepluse-streamlit-app.loadbalancer.server.port=8501"
        - "traefik.http.services.codepluse-streamlit-app.loadbalancer.healthcheck.path=/healthz"
        - "traefik.http.services.codepluse-streamlit-app.loadbalancer.healthcheck.interval=30s"

        # Middleware for security headers
        - "traefik.http.routers.codepluse-streamlit-app.middlewares=security-headers"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

networks:
  internal:
    driver: overlay
    internal: true
  traefik_main:
    external: true

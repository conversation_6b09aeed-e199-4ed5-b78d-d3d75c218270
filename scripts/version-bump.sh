#!/bin/bash

# Version bump script for auto-incrementing minor version
# Major version is fixed at 0, minor version auto-increments

set -e

# Get current version from pyproject.toml
CURRENT_VERSION=$(grep -E '^version = ' pyproject.toml | sed 's/version = "\(.*\)"/\1/')

if [ -z "$CURRENT_VERSION" ]; then
    echo "Error: Could not find version in pyproject.toml"
    exit 1
fi

echo "Current version: $CURRENT_VERSION"

# Parse version components
IFS='.' read -r major minor patch <<< "$CURRENT_VERSION"

# Ensure major version is 0
if [ "$major" != "0" ]; then
    echo "Warning: Major version is not 0, setting to 0"
    major=0
fi

# Increment minor version
minor=$((minor + 1))

# Reset patch version to 0 when incrementing minor
patch=0

NEW_VERSION="$major.$minor.$patch"

echo "New version: $NEW_VERSION"

# Update version in pyproject.toml
sed -i "s/version = \".*\"/version = \"$NEW_VERSION\"/" pyproject.toml

# Create git tag
git tag "v$NEW_VERSION"

echo "Version bumped to $NEW_VERSION and tag v$NEW_VERSION created"
echo "NEW_VERSION=$NEW_VERSION" >> $GITHUB_OUTPUT
echo "NEW_TAG=v$NEW_VERSION" >> $GITHUB_OUTPUT

# Production Docker Compose override
# Usage: docker compose -f compose.yaml -f compose.prod.yml up -d

services:
  agent_service:
    image: dockerhub.csharpp.com/codepluse/agent_service:${VERSION:-latest}
    restart: unless-stopped
    # Remove build context, use pre-built image
    build: null
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
      - LANGSMITH_API_KEY=${LANGSMITH_API_KEY:-}
      - LANGSMITH_PROJECT=${LANGSMITH_PROJECT:-}
    # Add resource limits
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    networks:
      - internal
      - web
    labels:
      # Traefik labels for reverse proxy (if using Traefik)
      - "traefik.enable=true"
      - "traefik.http.routers.agent-service.rule=Host(`api.yourdomain.com`)"
      - "traefik.http.routers.agent-service.tls=true"
      - "traefik.http.routers.agent-service.tls.certresolver=letsencrypt"
      - "traefik.http.services.agent-service.loadbalancer.server.port=8080"

  streamlit_app:
    image: dockerhub.csharpp.com/codepluse/streamlit_app:${VERSION:-latest}
    restart: unless-stopped
    # Remove build context, use pre-built image
    build: null
    environment:
      - AGENT_URL=http://agent_service:8080
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    # Add resource limits
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    networks:
      - internal
      - web
    labels:
      # Traefik labels for reverse proxy (if using Traefik)
      - "traefik.enable=true"
      - "traefik.http.routers.streamlit-app.rule=Host(`app.yourdomain.com`)"
      - "traefik.http.routers.streamlit-app.tls=true"
      - "traefik.http.routers.streamlit-app.tls.certresolver=letsencrypt"
      - "traefik.http.services.streamlit-app.loadbalancer.server.port=8501"

networks:
  internal:
    driver: bridge
    internal: true
  web:
    external: true

import math
import re
from typing import Any, List

import numexpr
from langchain_chroma import Chroma
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain_core.tools import tool
from langchain_core.vectorstores import VectorStoreRetriever


@tool
def calculator(expression: str) -> str:
    """Calculates a math expression using numexpr.

    Useful for when you need to answer questions about math using numexpr.
    This tool is only for math questions and nothing else. Only input
    math expressions.

    Args:
        expression (str): A valid numexpr formatted math expression.

    Returns:
        str: The result of the math expression.
    """

    try:
        local_dict = {"pi": math.pi, "e": math.e}
        output = str(
            numexpr.evaluate(
                expression.strip(),
                global_dict={},  # restrict access to globals
                local_dict=local_dict,  # add common mathematical functions
            )
        )
        return re.sub(r"^\[|\]$", "", output)
    except Exception as e:
        raise ValueError(
            f'calculator("{expression}") raised error: {e}.'
            " Please try again with a valid numerical expression"
        )

# calculator is now the tool created by the @tool decorator


# Format retrieved documents
def format_contexts(docs: List[Any]) -> str:
    return "\n\n".join(doc.page_content for doc in docs)


def load_chroma_db() -> VectorStoreRetriever:
    # Create the embedding function for our project description database
    try:
        # Use HuggingFace embeddings (local model)
        embeddings = HuggingFaceEmbeddings(
            model_name="all-MiniLM-L6-v2",  # Small, fast model that works well
            model_kwargs={'device': 'cpu'},
            encode_kwargs={'normalize_embeddings': True}
        )
    except Exception as e:
        raise RuntimeError(
            "Failed to initialize HuggingFaceEmbeddings. Make sure the model is installed."
        ) from e

    # Load the stored vector database
    chroma_db = Chroma(persist_directory="./chroma_db", embedding_function=embeddings)
    retriever = chroma_db.as_retriever(search_kwargs={"k": 5})
    return retriever


@tool
def database_search(query: str) -> str:
    """Searches the company's knowledge base stored in ChromaDB for relevant information.
    
    This tool searches through the company's handbook and documentation stored in a 
    ChromaDB vector database to find relevant information based on the query.
    
    Args:
        query (str): The search query to find relevant information in the knowledge base.
        
    Returns:
        str: Formatted text containing relevant documents found in the database.
    """
    # Get the chroma retriever
    retriever = load_chroma_db()

    # Search the database for relevant documents
    documents = retriever.invoke(query)

    # Format the documents into a string
    context_str = format_contexts(documents)

    return context_str


# database_search is now the tool created by the @tool decorator

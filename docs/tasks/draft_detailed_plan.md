Assumptions:

* Team size: 6 members (mix of FE, BE, AI/ML, Full Stack)
* Capacity per dev per week: \~32 hours (accounting for meetings, testing, review)
* Total capacity per sprint (Phase 1): 6 devs × 32 hrs = 192 hrs/sprint
* Goal for Phase 1: Implement at least 50% of core functionality (frontend + backend + agent flow MVP)

—

# 📌 Phase 1: 3 Weeks (3 Sprints) – MVP

Goal: Build working pipeline from input → skill sync → roadmap → report (first version)

### 🚀 Sprint 1 (Week 1): Core Input + Skill Sync Flow

| Task                                       | Est. Hours | Assigned To    |
| ------------------------------------------ | ---------- | -------------- |
| Setup project scaffolding (FE+BE)          | 10         | 2 Full Stack   |
| SSO Integration with FSOFT (mocked OK)     | 8          | Backend Dev    |
| Create Learning Request Form (FE)          | 10         | Frontend Dev   |
| Backend API: Submit Learning Request       | 10         | Backend Dev    |
| Skill Sync Engine: Connect to mock data    | 16         | Backend Dev    |
| Skill Sync Scheduler (weekly/manual)       | 8          | Backend Dev    |
| Skill Profile Viewer (FE)                  | 10         | Frontend Dev   |
| DB Models: Learning Request, Skill Profile | 6          | Full Stack Dev |
| Notifications (mock trigger)               | 6          | Full Stack Dev |
| Unit test coverage                         | 8          | All (rotated)  |

🕒 Sprint Total: \~92 hrs FE, \~88 hrs BE = 180 hrs

---

### 🚀 Sprint 2 (Week 2): Gap Analysis + Roadmap Generation (Basic)

| Task                                             | Est. Hours | Assigned To      |
| ------------------------------------------------ | ---------- | ---------------- |
| Build Gap Analysis Engine (logic + UI)           | 16         | Backend + AI Dev |
| Build RAG-based Target Profile Parser            | 12         | AI/ML Dev        |
| Store & view Gap Results                         | 6          | Backend + FE     |
| Learning Roadmap Generator (initial AI prompt)   | 16         | AI Dev + Backend |
| Display Roadmap Viewer (timeline basic)          | 12         | Frontend Dev     |
| DB Models: Gap Analysis, Target Profile, Roadmap | 6          | Backend Dev      |
| Agent Interaction Log (basic)                    | 8          | Backend Dev      |
| API: Fetch Gap/Roadmap                           | 6          | Backend Dev      |
| Unit + Integration Testing                       | 8          | All              |

🕒 Sprint Total: \~90 hrs AI/BE, \~76 hrs FE = 166 hrs

---

### 🚀 Sprint 3 (Week 3): Report Generator + Edits + Export

| Task                                    | Est. Hours | Assigned To    |
| --------------------------------------- | ---------- | -------------- |
| Agent Report Generator (Markdown)       | 10         | AI Dev         |
| Report Viewer (FE)                      | 8          | Frontend Dev   |
| Export to PDF                           | 6          | Full Stack Dev |
| Agent Report Editor (Manual edits)      | 10         | Full Stack Dev |
| Roadmap Edit UI                         | 10         | Frontend Dev   |
| Sync Skill Change Trigger → New Version | 6          | Backend Dev    |
| Notification for roadmap version        | 4          | Backend Dev    |
| Interaction Log Viewer                  | 6          | Frontend Dev   |
| Final QA & Bug Fix Round                | 8          | All            |

🕒 Sprint Total: \~66 hrs FE, \~52 hrs BE = 118 hrs

—

✅ Phase 1 Total Estimation: \~464 hrs
Delivered: Input → Skill Sync → Gap Analysis → Roadmap (Basic) → Agent Report MVP

—

# 📌 Phase 2: Full Delivery (2.5 Months Later)

Target: All remaining features + polish + integration + dashboards

⏳ Capacity: 6 members × 10 weeks × 32 hrs ≈ 1,920 hrs

### Key Phase 2 Task Buckets

| Task Area                                     | Est. Hours |
| --------------------------------------------- | ---------- |
| Role-based Access Control (User, Manager)     | 16         |
| RAG Enhancement: Course Metadata Filtering    | 20         |
| Admin Panel: RAG Config, Sync Schedule        | 20         |
| Skill Evolution Report (timeline UI)          | 20         |
| Usage Analytics Dashboard                     | 24         |
| Export Options (API, JSON format)             | 16         |
| Target Profile Viewer + Comparison UI         | 12         |
| Roadmap UX Polish (drag/drop, milestone edit) | 24         |
| Interaction Log Search + Export               | 16         |
| Report History Viewer                         | 12         |
| Gap Analysis Visualizations (charts, filters) | 16         |
| LMS Connector (optional)                      | 24         |
| Notification Settings Panel (User/Admin)      | 12         |
| Error Handling, Resilience                    | 16         |
| CI/CD Setup + Monitoring + Alerting           | 20         |
| Performance Optimization                      | 24         |
| Final QA, Regression Test                     | 24         |
| Production Readiness Checklist                | 16         |

🧠 Phase 2 Total Estimation: \~360–400 hrs core + buffer for iterations

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI-Powered Career Upskilling Workflow Requirements</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f9f9f9;
            color: #333;
        }
        .container {
            width: 90%;
            max-width: 1200px;
            margin: 20px auto;
            background-color: #fff;
            padding: 20px 30px; /* Increased padding */
            border-radius: 10px; /* Slightly more rounded */
            box-shadow: 0 4px 25px rgba(0,0,0,0.1); /* Softer, more modern shadow */
        }
        h1, h2, h3, h4 {
            color: #2c3e50; /* Dark blue-grey */
            margin-top: 1.5em;
            margin-bottom: 0.8em;
        }
        h1 {
            text-align: center;
            border-bottom: 3px solid #3498db; /* Accent color */
            padding-bottom: 15px;
            margin-bottom: 1.5em;
            font-size: 2.2em;
        }
        h2 {
            background-color: #3498db; /* Accent color */
            color: white;
            padding: 12px 18px;
            border-radius: 6px;
            margin-top: 50px; /* More spacing between major sections */
            font-size: 1.8em;
        }
        h3 {
            color: #2980b9; /* Darker accent */
            border-bottom: 2px solid #ecf0f1; /* Light grey */
            padding-bottom: 8px;
            font-size: 1.5em;
        }
        h4 {
            color: #16a085; /* Teal */
            font-size: 1.25em;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 25px;
            font-size: 0.95em; /* Slightly larger for readability */
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border-radius: 6px;
            overflow: hidden; /* Ensures border-radius applies to table */
        }
        th, td {
            border: 1px solid #dde4e9; /* Lighter border */
            padding: 12px 15px; /* More padding in cells */
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #f0f4f7; /* Very light blue-grey */
            font-weight: 600; /* Semi-bold */
            color: #34495e; /* Darker text for headers */
        }
        tr:nth-child(even) td { /* Apply to td for better consistency if th has different bg */
            background-color: #fbfcfd;
        }
        tr:hover td {
            background-color: #eaf5ff; /* Light blue hover */
        }
        .note, .clarification, .question-answered {
            padding: 18px;
            margin: 20px 0;
            border-left-width: 6px;
            border-left-style: solid;
            border-radius: 5px;
            background-color: #f8f9fa;
            font-size: 0.98em;
        }
        .note {
            border-left-color: #17a2b8; /* Info blue */
            background-color: #eef7f9;
        }
        .clarification {
            border-left-color: #ffc107; /* Warning yellow */
            background-color: #fff9e6;
        }
        .question-answered {
            border-left-color: #28a745; /* Success green */
            background-color: #eaf6ec;
        }
        .question-answered strong:first-child {
            color: #155724; /* Darker green for answered text */
        }
        blockquote {
            margin: 1.2em 0;
            padding: 0.8em 15px;
            background-color: #f1f3f5;
            border-left: 5px solid #adb5bd; /* Grey */
            font-style: italic;
        }
        .object-count, .function-summary {
            background-color: #e9ecef;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 25px;
        }
        .object-count p, .function-summary p {
            margin: 8px 0;
            font-size: 1.05em;
        }
        details {
            margin-bottom: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background-color: #fff;
        }
        details summary {
            cursor: pointer;
            font-weight: 600; /* Semi-bold */
            padding: 12px 15px;
            background-color: #f7f9fa; /* Lighter than table header */
            border-radius: 5px 5px 0 0; /* Rounded top corners */
            color: #2c3e50;
            transition: background-color 0.2s ease;
        }
        details summary:hover {
            background-color: #e9edf0;
        }
        details[open] summary {
            border-bottom: 1px solid #e0e0e0;
        }
        details > *:not(summary) {
            padding: 15px;
        }
        .toc {
            background-color: #eef7ff; /* Lighter blue */
            border: 1px solid #cce4ff; /* Softer border */
            padding: 20px;
            margin-bottom: 35px;
            border-radius: 8px;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc ul li {
            margin-bottom: 10px;
        }
        .toc ul li a {
            text-decoration: none;
            color: #0056b3;
            font-weight: 500;
        }
        .toc ul li a:hover {
            text-decoration: underline;
            color: #003d80;
        }
        .toc > ul > li > ul {
            padding-left: 25px; /* Indent sub-items more */
        }
        .toc > ul > li > ul > li {
            margin-bottom: 6px;
        }
        .icon {
            margin-right: 10px;
            font-size: 1.2em; /* Slightly larger icons */
            vertical-align: middle;
        }
        /* Specific styling for lists within report identification */
        #step5 ul {
            list-style-type: none;
            padding-left: 0;
        }
        #step5 ul li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        #step5 ul li::before {
            content: "▹"; /* Arrow-like bullet */
            position: absolute;
            left: 0;
            color: #3498db;
            font-weight: bold;
        }
        #step5 h4 { /* More spacing for report titles */
            margin-top: 1.5em;
        }

        /* Styles for Functional Overview Diagram */
        .functional-diagram-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 25px;
            margin: 30px auto;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            background-color: #fcfdff; /* Slightly off-white for depth */
            max-width: 900px; 
            box-shadow: 0 3px 15px rgba(0,0,0,0.07);
        }
        .diagram-node {
            background-color: #e9f5ff; 
            border: 1px solid #b3d9ff; 
            color: #1c5a8d; 
            padding: 18px 25px;
            border-radius: 8px;
            text-align: center;
            min-width: 300px; /* Increased min-width */
            max-width: 90%;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            margin: 12px 0; /* Increased margin */
            font-size: 1em;
            position: relative; 
        }
        .diagram-node .icon { /* Icon style already defined, but can be fine-tuned here if needed */
            margin-right: 8px;
            font-size: 1.3em; /* Ensure visibility */
        }
        .diagram-node .sub-text {
            font-size: 0.85em; /* Slightly larger sub-text */
            color: #5a7a9a; /* Softer color for sub-text */
            margin-top: 8px;
            line-height: 1.4;
        }
        .diagram-arrow-vertical {
            font-size: 2em; /* Prominent arrows */
            color: #3498db; 
            margin: 8px 0; /* Increased margin for arrows */
            font-weight: bold;
            line-height: 1; /* Ensure arrow doesn't take too much vertical space */
        }
        .diagram-parallel-group {
            display: flex;
            justify-content: space-evenly; /* Evenly space items */
            align-items: stretch; /* Make nodes same height if content differs */
            width: 100%;
            margin: 12px 0;
            gap: 20px; /* Gap between parallel nodes */
        }
        .diagram-parallel-group .diagram-node {
            min-width: 0; /* Allow flex to control width */
            flex: 1; /* Allow them to grow and shrink */
            /* margin is handled by gap */
        }
        .diagram-side-node {
            background-color: #fff8e1; 
            border: 1px solid #ffecb3; 
            color: #8d6e63; 
            padding: 15px 20px;
            border-radius: 8px;
            text-align: center;
            min-width: 280px; /* Adjusted min-width */
            max-width: 80%; /* Adjusted max-width */
            box-shadow: 0 2px 6px rgba(0,0,0,0.06);
            margin-top: 35px; /* More space above side node */
            border-left: 5px solid #ffd54f; 
        }
        .diagram-side-node .icon {
            margin-right: 8px;
            font-size: 1.3em;
        }
        .diagram-side-node .sub-text {
            font-size: 0.85em;
            color: #795548;
            margin-top: 6px;
        }
        #functional-overview-diagram { /* Style for the h3 title of the diagram */
            text-align: center;
            color: #2c3e50;
            font-size: 1.7em; /* Prominent title */
            margin-bottom: 25px; /* Space below title */
            margin-top: 40px; /* Space above title */
            border-bottom: none; /* Remove default h3 border if not desired here */
        }

    </style>
</head>
<body>
    <div class="container">
        <h1><span class="icon">🚀</span>AI-Powered Career Upskilling Workflow Requirements</h1>

        <nav class="toc" aria-labelledby="toc-heading">
            <h3 id="toc-heading" style="background-color: transparent; color: #2c3e50; padding: 0; margin-top:0; border:0; font-size: 1.6em;">Table of Contents</h3>
            <ul>
                <li><a href="#step1"><span class="icon">🗺️</span>Step 1: Business Process Mapping</a></li>
                <li><a href="#step2"><span class="icon">🧱</span>Step 2: Business Objects</a></li>
                <li><a href="#step3"><span class="icon">⚙️</span>Step 3: Functions</a></li>
                <li><a href="#step4"><span class="icon">🖥️</span>Step 4: Screens</a></li>
                <li><a href="#step5"><span class="icon">📄</span>Step 5: Report Identification</a></li>
                <li><a href="#step6"><span class="icon">📊</span>Step 6: Non-Functional Requirements (NFRs)</a></li>
                <li><a href="#step7"><span class="icon">🚧</span>Step 7: Constraints Identification</a></li>
            </ul>
        </nav>

        <section id="step1">
            <h2><span class="icon">🗺️</span>Step 1: Business Process Mapping</h2>
            <h3>High-Level Business Process Summary</h3>
            <p>This is a multi-actor workflow involving data ingestion, goal input, skill gap detection, roadmap generation, and reporting. The process starts with user input and ends with a final report after learning path review.</p>

            <h3 id="functional-overview-diagram">Functional Overview Diagram</h3>
            <div class="functional-diagram-container">
                <div class="diagram-node" id="node-login">
                    <span class="icon">👤</span> User Login (SSO with Fsoft)
                </div>
                <div class="diagram-arrow-vertical">↓</div>
                <div class="diagram-node" id="node-input">
                    <span class="icon">📝</span> Learning Constraints & Career Goals Input (User)
                    <div class="sub-text">Creates: Learning Request</div>
                </div>
                <div class="diagram-arrow-vertical">↓</div>
                <div class="diagram-parallel-group">
                    <div class="diagram-node" id="node-skill-fetch">
                        <span class="icon">🛠️</span> Fetch Existing Skills (System)
                        <div class="sub-text">Sources: Jira, OKR, iMocha, etc.<br>Updates: Skill Profile</div>
                    </div>
                    <div class="diagram-node" id="node-target-gen">
                        <span class="icon">🎯</span> Generate Target Profile (System - RAG)
                        <div class="sub-text">Uses: Career Goals, Benchmarks<br>Creates: Target Profile</div>
                    </div>
                </div>
                <div class="diagram-arrow-vertical">↓ <span style="font-size: 0.6em; color: #555; font-weight:normal;">(to Gap Analysis)</span></div>
                <div class="diagram-node" id="node-gap-analysis">
                    <span class="icon">🔍</span> Skill Gap Analysis (Agent/System)
                    <div class="sub-text">Compares: Skill Profile & Target Profile<br>Creates: Gap Analysis Details</div>
                </div>
                <div class="diagram-arrow-vertical">↓</div>
                <div class="diagram-node" id="node-roadmap-gen">
                    <span class="icon">🗺️</span> Learning Roadmap Generation (Agent/System - RAG)
                    <div class="sub-text">Uses: Gap Analysis, Learning Resources<br>Creates: Learning Roadmap</div>
                </div>
                <div class="diagram-arrow-vertical">↓</div>
                <div class="diagram-node" id="node-review">
                    <span class="icon">👀</span> Plan Review (Agent Supervisor)
                    <div class="sub-text">Optional: Revision by Agent</div>
                </div>
                <div class="diagram-arrow-vertical">↓</div>
                <div class="diagram-node" id="node-report-gen">
                    <span class="icon">✍️</span> Final Report Summary (Agent Summary Writer/System)
                    <div class="sub-text">Formats: Markdown, PDF<br>Creates: Agent Report</div>
                </div>
                <div class="diagram-arrow-vertical">↓</div>
                <div class="diagram-node" id="node-delivery">
                    <span class="icon">📤</span> Report Delivery & Access
                    <div class="sub-text">Via: UI, API, File Export</div>
                </div>
            
                <div class="diagram-side-node" id="node-interaction-log">
                    <span class="icon">💬</span> Interaction Log (System)
                    <div class="sub-text">Ongoing: Logs User-Agent clarifications, data prompts, etc.</div>
                </div>
            </div>

            <h3>State Machine Table: AI-Powered Career Upskilling Workflow</h3>
            <table>
                <thead>
                    <tr>
                        <th>Current State</th>
                        <th>Actor</th>
                        <th>Action</th>
                        <th>Next State</th>
                        <th>Remark</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Idle</td>
                        <td>User</td>
                        <td>Log in (SSO with Fsoft)</td>
                        <td>Authenticated</td>
                        <td>Start of user session</td>
                    </tr>
                    <tr>
                        <td>Authenticated</td>
                        <td>User</td>
                        <td>Enter learning constraint & career goals</td>
                        <td>PR Created</td>
                        <td>User input includes free-text goals and constraints</td>
                    </tr>
                    <tr>
                        <td>PR Created</td>
                        <td>System</td>
                        <td>Automatically fetch existing skill data from multiple sources</td>
                        <td>Skill Profile Fetched</td>
                        <td>Sources include Jira, OKR, iMocha, Coursera, etc.</td>
                    </tr>
                    <tr>
                        <td>Skill Profile Fetched</td>
                        <td>Agent Supervisor</td>
                        <td>Forward to Agent for gap analysis</td>
                        <td>Gap Analysis In Progress</td>
                        <td>Triggers Agent workflow</td>
                    </tr>
                    <tr>
                        <td>Gap Analysis In Progress</td>
                        <td>Agent</td>
                        <td>Determine skill gap vs. target (ask user if info is missing)</td>
                        <td>Gap Analysis Complete</td>
                        <td>Optional interaction loop with user</td>
                    </tr>
                    <tr>
                        <td>Gap Analysis Complete</td>
                        <td>Agent</td>
                        <td>Generate learning roadmap & recommend resources</td>
                        <td>Roadmap Generated</td>
                        <td>Includes courses, study plans, learning time estimation</td>
                    </tr>
                    <tr>
                        <td>Roadmap Generated</td>
                        <td>Agent Supervisor</td>
                        <td>Review generated plan</td>
                        <td>Plan Reviewed</td>
                        <td>May return to agent for revision</td>
                    </tr>
                    <tr>
                        <td>Plan Reviewed</td>
                        <td>Agent Summary Writer</td>
                        <td>Write final report summary</td>
                        <td>Report Ready</td>
                        <td>Final output formatted for delivery or integration</td>
                    </tr>
                    <tr>
                        <td>Report Ready</td>
                        <td>System</td>
                        <td>Expose report via UI / API / File export</td>
                        <td>Delivered</td>
                        <td>May support external system integration</td>
                    </tr>
                </tbody>
            </table>
            <div class="note">
                <p>🔁 <strong>Branch Note</strong>: If input data is incomplete, agent may prompt user again (loop back to PR Created).</p>
            </div>
            <div class="clarification">
                <p>❓ <strong>Clarification Needed</strong>: Should rejected/revised plans be stored with versioning (for audit trail)? Marked for Q&A.</p>
            </div>
        </section>

        <section id="step2">
            <h2><span class="icon">🧱</span>Step 2: Business Objects</h2>
            <table>
                <thead>
                    <tr>
                        <th>Object Name</th>
                        <th>Description</th>
                        <th>Classification</th>
                        <th>Source</th>
                        <th>Assumption</th>
                        <th>Question</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>User Profile</strong></td>
                        <td>Represents authenticated users (employees at Fsoft)</td>
                        <td>Supporting</td>
                        <td>Default system requirement</td>
                        <td>SSO integration with Fsoft is assumed</td>
                        <td>–</td>
                    </tr>
                    <tr>
                        <td><strong>Learning Request</strong></td>
                        <td>Input from user including career goal and learning constraints</td>
                        <td>Primary</td>
                        <td>Mindmap + Diagram + Requirement Text</td>
                        <td>Contains free-text goal and weekly learning time (constraint)</td>
                        <td>–</td>
                    </tr>
                    <tr>
                        <td><strong>Skill Profile</strong></td>
                        <td>Aggregated current skills of user from internal sources</td>
                        <td>Primary</td>
                        <td>Diagram + Mindmap</td>
                        <td>Aggregation is synchronous on request and also updated weekly with versioning and user notification</td>
                        <td class="question-answered">❓ Originally asked: Is the aggregation from external systems synchronous or near real-time? <br> ✅ <strong>Answer</strong>: Yes, on-demand + weekly sync with notifications and versioning.</td>
                    </tr>
                    <tr>
                        <td><strong>Target Profile</strong></td>
                        <td>Parsed goals and expected future capabilities</td>
                        <td>Supporting</td>
                        <td>Diagram + Mindmap</td>
                        <td>Derived using RAG (Retrieval-Augmented Generation) over benchmark role/skill documents</td>
                        <td class="question-answered">❓ Originally asked: Are standardized role/skill benchmarks maintained internally or by 3rd party? <br> ✅ <strong>Answer</strong>: Maintained via RAG.</td>
                    </tr>
                    <tr>
                        <td><strong>Gap Analysis</strong></td>
                        <td>Comparison between current and target skill sets</td>
                        <td>Supporting</td>
                        <td>Diagram</td>
                        <td>Detail is stored per session for audit and future review</td>
                        <td class="question-answered">❓ Originally asked: Should gap analysis detail be stored per session? <br> ✅ <strong>Answer</strong>: Yes.</td>
                    </tr>
                    <tr>
                        <td><strong>Learning Roadmap</strong></td>
                        <td>Curated sequence of steps (skills + resources) for upskilling</td>
                        <td>Primary</td>
                        <td>Diagram + Requirement Text</td>
                        <td>Editable after report is generated by users or managers</td>
                        <td class="question-answered">❓ Originally asked: Should the roadmap be editable before finalization? <br> ✅ <strong>Answer</strong>: Yes.</td>
                    </tr>
                    <tr>
                        <td><strong>Learning Resource</strong></td>
                        <td>Content items (courses, articles) pulled from public/internal databases</td>
                        <td>Supporting</td>
                        <td>Mindmap + Text</td>
                        <td>Resources are retrieved via RAG based on skill needs; no manual filtering required by users</td>
                        <td class="question-answered">❓ Originally asked: Should resource preference be configurable? <br> ✅ <strong>Answer</strong>: No; preference is managed via RAG.</td>
                    </tr>
                    <tr>
                        <td><strong>Agent Report</strong></td>
                        <td>Final summary of learning plan for export or integration</td>
                        <td>Primary</td>
                        <td>Diagram</td>
                        <td>Default output is Markdown, with export options (PDF, API, etc.)</td>
                        <td class="question-answered">❓ Originally asked: Is report formatting customizable? <br> ✅ <strong>Answer</strong>: Yes, Markdown + PDF.</td>
                    </tr>
                    <tr>
                        <td><strong>Interaction Log</strong></td>
                        <td>Tracks question/response loops between agent and user (if more info needed)</td>
                        <td>Supporting</td>
                        <td>Diagram</td>
                        <td>Logs are retained long-term for transparency and traceability</td>
                        <td class="question-answered">❓ Originally asked: Should logs be retained or purged? <br> ✅ <strong>Answer</strong>: Retained long-term.</td>
                    </tr>
                </tbody>
            </table>
            <div class="object-count">
                <h3><span class="icon">✅</span>Business Object Count</h3>
                <p><strong>Primary Objects</strong>: 4 (<code>Learning Request</code>, <code>Skill Profile</code>, <code>Learning Roadmap</code>, <code>Agent Report</code>)</p>
                <p><strong>Supporting Objects</strong>: 5 (<code>User Profile</code>, <code>Target Profile</code>, <code>Gap Analysis</code>, <code>Learning Resource</code>, <code>Interaction Log</code>)</p>
            </div>
        </section>

        <section id="step3">
            <h2><span class="icon">⚙️</span>Step 3: Functions</h2>
            <div class="function-summary">
                <h3><span class="icon">✅</span>Summary of Function Effort by Object</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Object</th>
                            <th>Total Functions</th>
                            <th>Total Effort (hrs)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td>Learning Request</td><td>6</td><td>8</td></tr>
                        <tr><td>Skill Profile</td><td>6</td><td>10</td></tr>
                        <tr><td>Target Profile</td><td>3</td><td>5</td></tr>
                        <tr><td>Gap Analysis</td><td>4</td><td>7</td></tr>
                        <tr><td>Learning Roadmap</td><td>5</td><td>10</td></tr>
                        <tr><td>Agent Report</td><td>5</td><td>8.5</td></tr>
                        <tr><td>Interaction Log</td><td>5</td><td>6</td></tr>
                        <tr><td><strong>TOTAL</strong></td><td><strong>34 functions</strong></td><td><strong>54.5 hrs</strong></td></tr>
                    </tbody>
                </table>
            </div>

            <details>
                <summary><span class="icon">🛠️</span>Functions for <strong>Learning Request</strong></summary>
                <table>
                    <thead><tr><th>Function Name</th><th>Description</th><th>Complexity</th><th>Function Type</th><th>BA Effort (hrs)</th><th>Related Object</th><th>Source</th><th>Assumption</th><th>Question</th></tr></thead>
                    <tbody>
                        <tr><td><strong>Create Learning Request</strong></td><td>User submits learning goals and constraints (e.g. weekly hours, deadline)</td><td>Medium</td><td>Basic</td><td>2</td><td>Learning Request</td><td>Mindmap + Diagram</td><td>Input is free-text and constraint fields</td><td>–</td></tr>
                        <tr><td><strong>Update Learning Request</strong></td><td>Edit an existing learning request before report is finalized</td><td>Low</td><td>Basic</td><td>1</td><td>Learning Request</td><td>Clarified requirement</td><td>Allowed only if roadmap/report not yet finalized</td><td>–</td></tr>
                        <tr><td><strong>View Learning Request</strong></td><td>Retrieve user-submitted learning request</td><td>Low</td><td>Basic</td><td>0.5</td><td>Learning Request</td><td>Implied</td><td>Used by both user and agent</td><td>–</td></tr>
                        <tr><td><strong>Submit Learning Request</strong></td><td>Initiate processing and analysis pipeline (send to agent supervisor)</td><td>Medium</td><td>Workflow</td><td>2</td><td>Learning Request</td><td>Diagram flow</td><td>Triggers downstream skill sync, gap analysis, and roadmap generation</td><td>–</td></tr>
                        <tr><td><strong>Version Learning Request</strong></td><td>Automatically create new version when user resubmits with changes</td><td>Medium</td><td>Support</td><td>1.5</td><td>Learning Request</td><td>Assumed based on versioning in flow</td><td>Versioned to keep history of roadmap impact</td><td>–</td></tr>
                        <tr><td><strong>Archive Learning Request</strong></td><td>Mark old/inactive learning requests for storage or deletion</td><td>Low</td><td>Support</td><td>1</td><td>Learning Request</td><td>Common practice</td><td>Auto-archived after roadmap export or inactivity</td><td>–</td></tr>
                    </tbody>
                </table>
            </details>

            <details>
                <summary><span class="icon">🛠️</span>Functions for <strong>Skill Profile</strong></summary>
                <table>
                    <thead><tr><th>Function Name</th><th>Description</th><th>Complexity</th><th>Function Type</th><th>BA Effort (hrs)</th><th>Related Object</th><th>Source</th><th>Assumption</th><th>Question</th></tr></thead>
                    <tbody>
                        <tr><td><strong>Fetch Skill Profile</strong></td><td>Retrieve current user skills from integrated systems like Jira, OKR, iMocha, etc.</td><td>High</td><td>Workflow</td><td>3</td><td>Skill Profile</td><td>Diagram + Mindmap</td><td>Pulls data from multiple sources via connectors</td><td>–</td></tr>
                        <tr><td><strong>Sync Skill Profile</strong></td><td>Manually trigger on-demand synchronization of skill data</td><td>Medium</td><td>Workflow</td><td>2</td><td>Skill Profile</td><td>Clarified (sync-on-request)</td><td>Can be initiated by user or system</td><td>–</td></tr>
                        <tr><td><strong>Schedule Skill Sync</strong></td><td>Periodically update skill profile every week</td><td>Medium</td><td>Cron Job</td><td>1.5</td><td>Skill Profile</td><td>Clarified requirement</td><td>Weekly batch update runs independently</td><td>–</td></tr>
                        <tr><td><strong>Version Skill Profile</strong></td><td>Store versioned snapshots when skill profile changes significantly</td><td>Medium</td><td>Support</td><td>1.5</td><td>Skill Profile</td><td>Clarified</td><td>Supports roadmap version comparison and user notifications</td><td>–</td></tr>
                        <tr><td><strong>Notify Skill Change</strong></td><td>Alert user via email and/or system notification when skill profile changes</td><td>Low</td><td>Support</td><td>1</td><td>Skill Profile</td><td>Clarified</td><td>Triggered post-sync if a change is detected</td><td>–</td></tr>
                        <tr><td><strong>View Skill Profile</strong></td><td>View current skill snapshot and history versions</td><td>Low</td><td>Basic</td><td>1</td><td>Skill Profile</td><td>Implied</td><td>Supports both user and agent access</td><td>–</td></tr>
                    </tbody>
                </table>
            </details>
            
            <details>
                <summary><span class="icon">🛠️</span>Functions for <strong>Target Profile</strong></summary>
                <table>
                    <thead><tr><th>Function Name</th><th>Description</th><th>Complexity</th><th>Function Type</th><th>BA Effort (hrs)</th><th>Related Object</th><th>Source</th><th>Assumption</th><th>Question</th></tr></thead>
                    <tbody>
                        <tr><td><strong>Generate Target Profile</strong></td><td>Use RAG (Retrieval-Augmented Generation) to create desired skill profile from career goal input</td><td>High</td><td>Workflow</td><td>3</td><td>Target Profile</td><td>Diagram + Clarified</td><td>Generated from free-text goal using internal or 3rd-party role/skill benchmarks</td><td>–</td></tr>
                        <tr><td><strong>Update Target Profile</strong></td><td>Refresh or regenerate the target profile if user changes learning goal</td><td>Medium</td><td>Workflow</td><td>1.5</td><td>Target Profile</td><td>Inferred from process</td><td>Auto-refresh on learning request update</td><td>–</td></tr>
                        <tr><td><strong>View Target Profile</strong></td><td>Allow user and agent to inspect expected future skill set</td><td>Low</td><td>Basic</td><td>0.5</td><td>Target Profile</td><td>Implied</td><td>Visualized alongside gap analysis or roadmap</td><td>–</td></tr>
                    </tbody>
                </table>
            </details>

            <details>
                <summary><span class="icon">🛠️</span>Functions for <strong>Gap Analysis</strong></summary>
                <table>
                    <thead><tr><th>Function Name</th><th>Description</th><th>Complexity</th><th>Function Type</th><th>BA Effort (hrs)</th><th>Related Object</th><th>Source</th><th>Assumption</th><th>Question</th></tr></thead>
                    <tbody>
                        <tr><td><strong>Run Gap Analysis</strong></td><td>Compare Skill Profile with Target Profile to identify missing or underdeveloped skills</td><td>High</td><td>Workflow</td><td>3</td><td>Gap Analysis</td><td>Diagram + Clarified</td><td>Involves vectorized matching, semantic comparison, and may require querying user for missing input</td><td>–</td></tr>
                        <tr><td><strong>Log Gap Analysis Result</strong></td><td>Store result and metadata for audit and future roadmap reference</td><td>Medium</td><td>Support</td><td>1.5</td><td>Gap Analysis</td><td>Clarified</td><td>Stores skill delta with timestamp and reference IDs</td><td>–</td></tr>
                        <tr><td><strong>View Gap Analysis</strong></td><td>Display the comparison result between current and target profiles</td><td>Low</td><td>Basic</td><td>1</td><td>Gap Analysis</td><td>Implied</td><td>Used by agent and user to understand recommendation logic</td><td>–</td></tr>
                        <tr><td><strong>Retrieve Past Analyses</strong></td><td>Access past gap analysis sessions for the same user</td><td>Medium</td><td>Support</td><td>1.5</td><td>Gap Analysis</td><td>Clarified</td><td>Used for longitudinal tracking or troubleshooting</td><td>–</td></tr>
                    </tbody>
                </table>
            </details>

            <details>
                <summary><span class="icon">🛠️</span>Functions for <strong>Learning Roadmap</strong></summary>
                <table>
                    <thead><tr><th>Function Name</th><th>Description</th><th>Complexity</th><th>Function Type</th><th>BA Effort (hrs)</th><th>Related Object</th><th>Source</th><th>Assumption</th><th>Question</th></tr></thead>
                    <tbody>
                        <tr><td><strong>Generate Learning Roadmap</strong></td><td>Automatically build personalized roadmap from gap analysis using RAG and learning resources</td><td>High</td><td>Workflow</td><td>3.5</td><td>Learning Roadmap</td><td>Diagram + Mindmap</td><td>Structured by milestones, timeline, resource type, and estimated duration</td><td>–</td></tr>
                        <tr><td><strong>Edit Learning Roadmap</strong></td><td>Allow user or manager to modify generated roadmap post-report</td><td>Medium</td><td>Workflow</td><td>2</td><td>Learning Roadmap</td><td>Clarified</td><td>Editable only after initial roadmap is generated</td><td>–</td></tr>
                        <tr><td><strong>Version Learning Roadmap</strong></td><td>Store new version whenever Skill Profile or Learning Request changes</td><td>Medium</td><td>Support</td><td>1.5</td><td>Learning Roadmap</td><td>Clarified</td><td>Used for historical comparison and audit</td><td>–</td></tr>
                        <tr><td><strong>View Learning Roadmap</strong></td><td>Display roadmap in structured layout with filtering/grouping (e.g. by skill domain or resource type)</td><td>Low</td><td>Basic</td><td>1.5</td><td>Learning Roadmap</td><td>Assumed UI pattern</td><td>Includes roadmap timeline view, skill track view, or exportable layout</td><td>–</td></tr>
                        <tr><td><strong>Export Learning Roadmap</strong></td><td>Convert roadmap to file (Markdown/PDF) or expose via API</td><td>Medium</td><td>Support</td><td>1.5</td><td>Learning Roadmap</td><td>Clarified (via report)</td><td>Format consistent with Agent Report, supports external system use</td><td>–</td></tr>
                    </tbody>
                </table>
            </details>

            <details>
                <summary><span class="icon">🛠️</span>Functions for <strong>Agent Report</strong></summary>
                <table>
                    <thead><tr><th>Function Name</th><th>Description</th><th>Complexity</th><th>Function Type</th><th>BA Effort (hrs)</th><th>Related Object</th><th>Source</th><th>Assumption</th><th>Question</th></tr></thead>
                    <tbody>
                        <tr><td><strong>Generate Agent Report</strong></td><td>Compose final upskilling summary based on roadmap, gap analysis, and metadata</td><td>High</td><td>Workflow</td><td>3</td><td>Agent Report</td><td>Diagram</td><td>Content auto-generated in markdown format with summary logic</td><td>–</td></tr>
                        <tr><td><strong>Edit Agent Report</strong></td><td>Allow supervisor or summary-writer to refine language or structure before finalization</td><td>Medium</td><td>Workflow</td><td>2</td><td>Agent Report</td><td>Mindmap</td><td>Optional manual editing before export</td><td>–</td></tr>
                        <tr><td><strong>Export Agent Report</strong></td><td>Provide options to download or send the report (Markdown, PDF, API)</td><td>Medium</td><td>Support</td><td>1.5</td><td>Agent Report</td><td>Clarified requirement</td><td>Exported formats: Markdown for user, PDF for formal use, JSON for API</td><td>–</td></tr>
                        <tr><td><strong>View Agent Report</strong></td><td>Allow user and stakeholder to view the final approved version</td><td>Low</td><td>Basic</td><td>1</td><td>Agent Report</td><td>Implied</td><td>Read-only; includes metadata like version, generated date</td><td>–</td></tr>
                        <tr><td><strong>Log Report Metadata</strong></td><td>Store metadata (creator, version, timestamps) for audit or reference</td><td>Low</td><td>Support</td><td>1</td><td>Agent Report</td><td>Common best practice</td><td>Metadata stored alongside exported file or API version</td><td>–</td></tr>
                    </tbody>
                </table>
            </details>

            <details>
                <summary><span class="icon">🛠️</span>Functions for <strong>Interaction Log</strong></summary>
                <table>
                    <thead><tr><th>Function Name</th><th>Description</th><th>Complexity</th><th>Function Type</th><th>BA Effort (hrs)</th><th>Related Object</th><th>Source</th><th>Assumption</th><th>Question</th></tr></thead>
                    <tbody>
                        <tr><td><strong>Log Agent Interaction</strong></td><td>Record each clarification question and response between the user and the agent</td><td>Medium</td><td>Support</td><td>1.5</td><td>Interaction Log</td><td>Diagram</td><td>Captures question asked, user response, and timestamp</td><td>–</td></tr>
                        <tr><td><strong>View Interaction Log</strong></td><td>Display full interaction history for transparency or review</td><td>Low</td><td>Basic</td><td>1</td><td>Interaction Log</td><td>Implied</td><td>Users and system stakeholders can review dialog history</td><td>–</td></tr>
                        <tr><td><strong>Search Interaction Log</strong></td><td>Filter or query interaction history by keyword, session, or object</td><td>Medium</td><td>Support</td><td>1.5</td><td>Interaction Log</td><td>Inferred from log usage</td><td>Helps in troubleshooting or regenerating outcomes</td><td>–</td></tr>
                        <tr><td><strong>Export Interaction Log</strong></td><td>Download interaction history as file (e.g., JSON or text) for compliance or report attachment</td><td>Low</td><td>Support</td><td>1</td><td>Interaction Log</td><td>Best practice</td><td>Used for audit, integration, or recordkeeping</td><td>–</td></tr>
                        <tr><td><strong>Retain Interaction Log</strong></td><td>System ensures long-term storage and traceability of all logs</td><td>Low</td><td>Cron Job</td><td>1</td><td>Interaction Log</td><td>Clarified</td><td>Retention aligned with org policy on skill evolution records</td><td>–</td></tr>
                    </tbody>
                </table>
            </details>
        </section>

        <section id="step4">
            <h2><span class="icon">🖥️</span>Step 4: Screens</h2>
            <details>
                <summary><span class="icon">🖼️</span>Screens for <strong>Learning Request</strong></summary>
                <table>
                    <thead><tr><th>Screen Name</th><th>Description</th><th># Components</th><th>Complexity</th><th>BA Effort (hrs)</th><th>Source</th><th>Assumption</th><th>Question</th></tr></thead>
                    <tbody>
                        <tr><td><strong>Learning Request Form</strong></td><td>Interface where user submits their career goal and constraints (e.g., learning hours/week)</td><td>6–8</td><td>Medium</td><td>2.5</td><td>Mindmap + Diagram</td><td>Free-text field for goal; input for constraints (dropdown or numeric fields)</td><td>–</td></tr>
                        <tr><td><strong>Edit Learning Request</strong></td><td>Form to revise existing request before submission or report generation</td><td>4–6</td><td>Low</td><td>1.5</td><td>Clarified</td><td>Reuses same components as original form</td><td>–</td></tr>
                        <tr><td><strong>Learning Request Summary</strong></td><td>Read-only view showing submitted request and metadata</td><td>4</td><td>Low</td><td>1</td><td>Implied</td><td>Used in dashboards or before generating report</td><td>–</td></tr>
                        <tr><td><strong>Learning Request Version History</strong></td><td>Lists all historical requests by user with timestamps and status</td><td>4–6</td><td>Medium</td><td>2</td><td>Versioning assumption</td><td>Includes comparison links and view buttons</td><td>–</td></tr>
                    </tbody>
                </table>
            </details>

            <details>
                <summary><span class="icon">🖼️</span>Screens for <strong>Skill Profile</strong></summary>
                <table>
                    <thead><tr><th>Screen Name</th><th>Description</th><th># Components</th><th>Complexity</th><th>BA Effort (hrs)</th><th>Source</th><th>Assumption</th><th>Question</th></tr></thead>
                    <tbody>
                        <tr><td><strong>Skill Profile Viewer</strong></td><td>Displays user's current skills with categorization (e.g., by domain or source system)</td><td>6–8</td><td>Medium</td><td>2.5</td><td>Mindmap + Diagram</td><td>Read-only view with optional filtering and grouping</td><td>–</td></tr>
                        <tr><td><strong>Skill Sync Console</strong></td><td>Interface to trigger manual sync and view sync history/status</td><td>4–6</td><td>Medium</td><td>2</td><td>Clarified requirement</td><td>Sync triggers immediate refresh and logs update source</td><td>–</td></tr>
                        <tr><td><strong>Skill Version History</strong></td><td>Displays timeline or table of previous skill snapshots with diff capability</td><td>4–6</td><td>Medium</td><td>2</td><td>Clarified</td><td>Supports comparison between versions before/after learning or sync</td><td>–</td></tr>
                        <tr><td><strong>Skill Change Notification Log</strong></td><td>View log of skill changes that triggered roadmap updates or alerts</td><td>4</td><td>Low</td><td>1.5</td><td>Notification assumption</td><td>Read-only; includes links to roadmap versions and alert timestamps</td><td>–</td></tr>
                    </tbody>
                </table>
            </details>

            <details>
                <summary><span class="icon">🖼️</span>Screens for <strong>Target Profile</strong></summary>
                <table>
                    <thead><tr><th>Screen Name</th><th>Description</th><th># Components</th><th>Complexity</th><th>BA Effort (hrs)</th><th>Source</th><th>Assumption</th><th>Question</th></tr></thead>
                    <tbody>
                        <tr><td>Target Profile Viewer</td><td>Displays system-generated desired skill set aligned with the user’s career goal</td><td>4–6</td><td>Low–Medium</td><td>1.5</td><td>Clarified + Diagram</td><td>Presented in categorized or leveled format (e.g., Beginner → Advanced)</td><td>–</td></tr>
                        <tr><td>Target Profile Comparison</td><td>Visual diff or side-by-side comparison of Skill Profile vs Target Profile</td><td>6–8</td><td>Medium</td><td>2</td><td>Gap Analysis stage</td><td>Used by users and agents to assess training needs</td><td>–</td></tr>
                        <tr><td>Target Profile Generation Log</td><td>Shows when and how the profile was generated (input, timestamp, RAG source link if applicable)</td><td>3–4</td><td>Low</td><td>1</td><td>RAG pipeline implied</td><td>Useful for transparency and debugging</td><td>–</td></tr>
                    </tbody>
                </table>
            </details>

            <details>
                <summary><span class="icon">🖼️</span>Screens for <strong>Gap Analysis</strong></summary>
                 <table>
                    <thead><tr><th>Screen Name</th><th>Description</th><th># Components</th><th>Complexity</th><th>BA Effort (hrs)</th><th>Source</th><th>Assumption</th><th>Question</th></tr></thead>
                    <tbody>
                        <tr><td>Gap Analysis Dashboard</td><td>Visual overview comparing current skills vs. target skills, grouped by category/domain</td><td>6–8</td><td>Medium</td><td>2.5</td><td>Diagram + Mindmap</td><td>May use bar charts, skill matrices, or heatmaps to highlight gaps</td><td>–</td></tr>
                        <tr><td>Gap Detail Viewer</td><td>Detailed view of individual gap items with metadata (source, severity, suggested actions)</td><td>5–7</td><td>Medium</td><td>2</td><td>Clarified</td><td>Includes links to associated learning resources and roadmap entries</td><td>–</td></tr>
                        <tr><td>Gap Analysis History</td><td>List of previous gap analysis sessions with timestamps, skill versions, and navigation controls</td><td>4–6</td><td>Medium</td><td>1.5</td><td>Clarified</td><td>Supports longitudinal tracking and auditability</td><td>–</td></tr>
                        <tr><td>Gap Export Panel</td><td>Export gap results to Markdown, JSON, or PDF for reporting or documentation</td><td>3–4</td><td>Low</td><td>1</td><td>Implied</td><td>Shares export logic with Agent Report</td><td>–</td></tr>
                    </tbody>
                </table>
            </details>

            <details>
                <summary><span class="icon">🖼️</span>Screens for <strong>Learning Roadmap</strong></summary>
                <table>
                    <thead><tr><th>Screen Name</th><th>Description</th><th># Components</th><th>Complexity</th><th>BA Effort (hrs)</th><th>Source</th><th>Assumption</th><th>Question</th></tr></thead>
                    <tbody>
                        <tr><td>Roadmap Overview</td><td>Main visual roadmap view (timeline or swimlane) with milestones, topics, and progress indicators</td><td>6–8</td><td>Medium–High</td><td>3</td><td>Mindmap + Diagram</td><td>Timeline is interactive; users can expand/collapse sections per skill category</td><td>–</td></tr>
                        <tr><td>Roadmap Editor</td><td>Interface for user or manager to modify recommended roadmap post-generation</td><td>6–8</td><td>Medium</td><td>2</td><td>Clarified edit support</td><td>Drag-and-drop or editable table of learning steps</td><td>–</td></tr>
                        <tr><td>Roadmap Version History</td><td>View of all roadmap versions tied to skill profile or goal changes</td><td>4–6</td><td>Medium</td><td>1.5</td><td>Clarified versioning</td><td>Includes timestamps, source change reason, and view/restore actions</td><td>–</td></tr>
                        <tr><td>Roadmap Resource Viewer</td><td>Detailed list of resources associated with roadmap (course title, platform, estimated time, etc)</td><td>4–6</td><td>Medium</td><td>1.5</td><td>Diagram + RAG integration</td><td>Users can filter by type (video, article, book) or by source (internal, Coursera, etc.)</td><td>–</td></tr>
                        <tr><td>Roadmap Export Panel</td><td>Enables export to Markdown/PDF/API and lets user choose formatting options</td><td>3–4</td><td>Low</td><td>1</td><td>Clarified</td><td>Shared logic with Agent Report for format conversion and delivery</td><td>–</td></tr>
                    </tbody>
                </table>
            </details>

            <details>
                <summary><span class="icon">🖼️</span>Screens for <strong>Agent Report</strong></summary>
                <table>
                    <thead><tr><th>Screen Name</th><th>Description</th><th># Components</th><th>Complexity</th><th>BA Effort (hrs)</th><th>Source</th><th>Assumption</th><th>Question</th></tr></thead>
                    <tbody>
                        <tr><td>Agent Report Viewer</td><td>Final view of the generated learning report in Markdown format</td><td>4–6</td><td>Medium</td><td>2</td><td>Clarified requirement</td><td>Includes skill summary, roadmap link, agent notes</td><td>–</td></tr>
                        <tr><td>Agent Report Editor</td><td>Allows manual revision of AI-generated summary by supervisor or writer</td><td>4–6</td><td>Medium</td><td>2</td><td>Clarified editability</td><td>Rich-text editor or Markdown-compatible form</td><td>–</td></tr>
                        <tr><td>Report Export Panel</td><td>Export the report to PDF or share via API</td><td>3–4</td><td>Low</td><td>1.5</td><td>Clarified export formats</td><td>May allow template selection or branding headers</td><td>–</td></tr>
                        <tr><td>Report Metadata Viewer</td><td>Shows report metadata (author, timestamp, related learning request & roadmap version)</td><td>3–4</td><td>Low</td><td>1</td><td>Clarified best practice</td><td>Part of versioning and auditability</td><td>–</td></tr>
                    </tbody>
                </table>
            </details>

            <details>
                <summary><span class="icon">🖼️</span>Screens for <strong>Interaction Log</strong></summary>
                <table>
                    <thead><tr><th>Screen Name</th><th>Description</th><th># Components</th><th>Complexity</th><th>BA Effort (hrs)</th><th>Source</th><th>Assumption</th><th>Question</th></tr></thead>
                    <tbody>
                        <tr><td>Interaction History Viewer</td><td>Chronological list of all agent-user clarification questions and responses</td><td>4–6</td><td>Medium</td><td>2</td><td>Clarified requirement</td><td>Includes timestamps, message type, and optionally skill object being discussed</td><td>–</td></tr>
                        <tr><td>Interaction Log Detail</td><td>Detailed modal or panel showing a full interaction session</td><td>4</td><td>Low</td><td>1.5</td><td>Diagram + Clarified</td><td>Enables quick navigation and context for each clarification round</td><td>–</td></tr>
                        <tr><td>Log Search & Filter Panel</td><td>Allows searching interaction logs by keyword, session ID, or related skill</td><td>4–6</td><td>Medium</td><td>1.5</td><td>Assumed UX best practice</td><td>Useful for audit, debug, and analysis</td><td>–</td></tr>
                        <tr><td>Export Log Panel</td><td>Export full or filtered logs to JSON or text format</td><td>3–4</td><td>Low</td><td>1</td><td>Clarified requirement</td><td>Used for compliance, audit trail, or integration with reporting pipelines</td><td>–</td></tr>
                    </tbody>
                </table>
            </details>
        </section>

        <section id="step5">
            <h2><span class="icon">📄</span>Step 5: Report Identification</h2>
            
            <h4><span class="icon">📰</span>Learning Path Summary Report</h4>
            <ul>
                <li><strong>Description</strong>: Final upskilling plan, generated per user request, detailing skill gaps, recommended learning roadmap, estimated effort, and resource links.</li>
                <li><strong>User</strong>: Employee (requester), Line Manager, PMO, HRBP</li>
                <li><strong>Purpose</strong>: Align personal development with project needs and organizational L&D goals</li>
                <li><strong>Format</strong>: Markdown (default), PDF (optional), API-exportable JSON</li>
                <li><strong>Frequency</strong>: On-demand per request or roadmap revision</li>
                <li><strong>Source Data</strong>: Learning Request, Skill Profile, Gap Analysis, Learning Roadmap</li>
            </ul>

            <h4><span class="icon">📈</span>Skill Profile Evolution Report</h4>
            <ul>
                <li><strong>Description</strong>: Chronological report showing how an individual’s skill set has changed over time</li>
                <li><strong>User</strong>: HRBP, Tech Leads, Employee</li>
                <li><strong>Purpose</strong>: Assess progress, readiness for new roles, and training effectiveness</li>
                <li><strong>Format</strong>: Table or chart view, exportable as CSV/PDF</li>
                <li><strong>Frequency</strong>: Weekly or on roadmap versioning</li>
                <li><strong>Source Data</strong>: Skill Profile versions, Learning Roadmap milestones</li>
            </ul>

            <h4><span class="icon">🔍</span>Gap Analysis Snapshot</h4>
            <ul>
                <li><strong>Description</strong>: Snapshot view of current vs. target skill deltas at a specific point in time</li>
                <li><strong>User</strong>: PMO, Team Leads, L&D Strategist</li>
                <li><strong>Purpose</strong>: Identify team-wide skill gaps for hiring/training priorities</li>
                <li><strong>Format</strong>: Matrix table, bar chart visualization</li>
                <li><strong>Frequency</strong>: Per skill sync or roadmap generation</li>
                <li><strong>Source Data</strong>: Gap Analysis, Skill Profile, Target Profile</li>
            </ul>

            <h4><span class="icon">📊</span>Usage & Interaction Analytics</h4>
            <ul>
                <li><strong>Description</strong>: Aggregated stats of user-agent interactions, roadmap completions, report generation trends</li>
                <li><strong>User</strong>: Product Owner, Platform Admin, L&D Operations</li>
                <li><strong>Purpose</strong>: Track adoption, optimize content matching, measure ROI</li>
                <li><strong>Format</strong>: Dashboard or PDF summary</li>
                <li><strong>Frequency</strong>: Monthly or quarterly</li>
                <li><strong>Source Data</strong>: Interaction Log, Report Logs, Sync Logs</li>
            </ul>

            <h4><span class="icon">💡</span>Learning Resource Effectiveness Report</h4>
            <ul>
                <li><strong>Description</strong>: Evaluates which external/internal courses are most selected or lead to skill progress</li>
                <li><strong>User</strong>: HR, Content Curators, Team Leads</li>
                <li><strong>Purpose</strong>: Refine resource pool and RAG dataset for roadmap generation</li>
                <li><strong>Format</strong>: CSV or dashboard</li>
                <li><strong>Frequency</strong>: Monthly</li>
                <li><strong>Source Data</strong>: Learning Roadmaps, Resource metadata, Skill Profile deltas</li>
            </ul>
        </section>

        <section id="step6">
            <h2><span class="icon">⚙️</span>Step 6: Non-Functional Requirements (NFRs)</h2>
            
            <h3><span class="icon">🧩</span>1. Performance Requirements</h3>
            <table>
                <thead><tr><th>Metric</th><th>Target</th><th>Notes</th></tr></thead>
                <tbody>
                    <tr><td>Skill sync response time</td><td>≤ 5 seconds (on-demand); ≤ 30 seconds (weekly batch)</td><td>Aggregates across multiple internal systems</td></tr>
                    <tr><td>Roadmap generation time</td><td>≤ 15 seconds (RAG + gap analysis + course curation)</td><td>Includes vector search and multi-agent response</td></tr>
                    <tr><td>Agent report rendering</td><td>≤ 3 seconds</td><td>Markdown rendering + export</td></tr>
                    <tr><td>Concurrent users supported</td><td>1,000+ simultaneous sessions</td><td>Internal enterprise scale</td></tr>
                </tbody>
            </table>

            <h3><span class="icon">🔒</span>2. Security Requirements</h3>
            <table>
                <thead><tr><th>Area</th><th>Specification</th></tr></thead>
                <tbody>
                    <tr><td>Authentication</td><td>SSO via FSOFT Identity Provider (e.g., OAuth2 / SAML)</td></tr>
                    <tr><td>Authorization</td><td>Role-based access (Employee, Supervisor, PMO, Admin)</td></tr>
                    <tr><td>Data encryption</td><td>All data encrypted in transit (TLS 1.2+) and at rest (AES-256)</td></tr>
                    <tr><td>PII protection</td><td>Mask sensitive personal information in logs and exports</td></tr>
                    <tr><td>Audit trail</td><td>All changes (skill profile, request, edits, exports) are logged and versioned</td></tr>
                </tbody>
            </table>

            <h3><span class="icon">🔄</span>3. Maintainability & Observability</h3>
            <table>
                <thead><tr><th>Area</th><th>Specification</th></tr></thead>
                <tbody>
                    <tr><td>Logging</td><td>Centralized logging of agent actions, skill sync events, user interactions</td></tr>
                    <tr><td>Monitoring</td><td>Real-time health checks + alerting (CPU/memory, API latency, failed syncs)</td></tr>
                    <tr><td>Versioning</td><td>Skill Profile, Roadmap, and Report version control with restore options</td></tr>
                    <tr><td>Configuration</td><td>Admin settings for resource weighting, sync intervals, thresholds</td></tr>
                </tbody>
            </table>

            <h3><span class="icon">🧠</span>4. Usability & UX</h3>
            <table>
                <thead><tr><th>Metric/Feature</th><th>Target</th></tr></thead>
                <tbody>
                    <tr><td>Time to submit request</td><td>≤ 2 minutes from login to roadmap generation</td></tr>
                    <tr><td>Device compatibility</td><td>Desktop-first; responsive support for tablets and wide mobile screens</td></tr>
                    <tr><td>Accessibility</td><td>WCAG 2.1 AA compliant interfaces (contrast, keyboard nav, alt text)</td></tr>
                    <tr><td>Feedback loop</td><td>Tooltips, agent explanations, “why this skill?” support in roadmap/gap UI</td></tr>
                </tbody>
            </table>

            <h3><span class="icon">🌍</span>5. Integration & Interoperability</h3>
            <table>
                <thead><tr><th>System</th><th>Integration Type</th><th>Notes</th></tr></thead>
                <tbody>
                    <tr><td>Jira, iMocha, OKR system</td><td>API connectors</td><td>For skill extraction</td></tr>
                    <tr><td>Coursera, Udemy, etc.</td><td>RAG-enabled search</td><td>Public content indexed, filtered by skill taxonomy</td></tr>
                    <tr><td>Internal LMS</td><td>Optional connector</td><td>To prioritize in-house resources</td></tr>
                    <tr><td>Notification Systems</td><td>Email, internal alert channels</td><td>For skill change or roadmap refresh alerts</td></tr>
                </tbody>
            </table>
        </section>

        <section id="step7">
            <h2><span class="icon">🚧</span>Step 7: Constraints Identification</h2>
            <p>This step outlines limitations, dependencies, or conditions that may affect implementation, design decisions, or delivery scope.</p>

            <h3><span class="icon">🧱</span>1. Technical Constraints</h3>
            <table>
                <thead><tr><th>Constraint</th><th>Description</th><th>Impact</th></tr></thead>
                <tbody>
                    <tr><td>Internal system data quality</td><td>Skill data pulled from Jira, OKRs, iMocha may be inconsistent or incomplete</td><td>Requires fallback questions to user</td></tr>
                    <tr><td>External API limitations</td><td>3rd-party content providers (Coursera, Udemy) may impose query rate limits or require tokens</td><td>Caching and asynchronous indexing</td></tr>
                    <tr><td>RAG cost/performance balance</td><td>RAG search and generation are resource-intensive (GPU/latency)</td><td>Optimize using pre-embedding and caching</td></tr>
                    <tr><td>LLM token limits</td><td>Some LLMs have token context limits (e.g., 8k–32k) that may affect multi-skill processing</td><td>Use chunked queries, summarization</td></tr>
                </tbody>
            </table>

            <h3><span class="icon">🔐</span>2. Organizational Constraints</h3>
            <table>
                <thead><tr><th>Constraint</th><th>Description</th><th>Impact</th></tr></thead>
                <tbody>
                    <tr><td>SSO & Role Setup</td><td>Must integrate with FSOFT authentication and internal permission models</td><td>Requires coordination with IT/Security</td></tr>
                    <tr><td>Data residency / compliance</td><td>Skill and user data must comply with internal governance & privacy policies</td><td>May restrict external API use</td></tr>
                    <tr><td>Internal LMS prioritization</td><td>Preference for internal content where available</td><td>RAG must allow configurable prioritization</td></tr>
                </tbody>
            </table>

            <h3><span class="icon">📆</span>3. Scheduling & Delivery Constraints</h3>
            <table>
                <thead><tr><th>Constraint</th><th>Description</th><th>Impact</th></tr></thead>
                <tbody>
                    <tr><td>HackAIthon #4 Timeline</td><td>Must deliver MVP/demo within event deadline</td><td>Feature prioritization is essential</td></tr>
                    <tr><td>Team resource availability</td><td>BA, Dev, and Design resources are part-time during hackathon</td><td>Focus on high-impact, low-effort items</td></tr>
                    <tr><td>Deployment scope</td><td>MVP targets internal sandbox or UAT, not full production</td><td>Simplified auth & mock data allowed</td></tr>
                </tbody>
            </table>
        </section>
    </div>
</body>
</html>

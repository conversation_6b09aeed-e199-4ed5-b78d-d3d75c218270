# Buổi thảo luận ngày 29-05-2025: Review Requirement và Detail Plan Dự Án

Dựa trên thông tin từ nguồn cung cấp, buổi thảo luận diễn ra vào ngày **29-05-2025** tập trung vào việc review requirement và detail plan cho giai đoạn phát triển dự án trong **3 tuần tới**. <PERSON><PERSON><PERSON> kị<PERSON>, buổi họp cũng sẽ review về UI ban đầu.

## Nội dung chính của buổi thảo luận bao gồm:

### 1. Sử dụng công cụ AI để hỗ trợ lập kế hoạch và requirement (Custom GPT)
* Hệ thống đang sử dụng một custom GPT do đội BA cung cấp để generate overview về các function và detail plan.
* Công cụ này hỗ trợ quy trình làm việc theo từng bướ<PERSON>, ví dụ:
    * **Đầu vào:** đề bài, file đ<PERSON><PERSON> kèm, ghi chú meeting
    * **Bước 1:** High level design (state machine, object)
    * **Bước 2:** Refine và trả lời làm rõ
    * **Bước 3:** Define function
    * ... cho đến non-functional requirement và plan.
* Mô hình này cũng hỗ trợ generate non-functional requirement (performance, security, maintainability, usability, integration, technical constraint) và business constraint.
* Công cụ này đã giúp chia kế hoạch 3 tuần thành các sprint (mỗi tuần một sprint) để phù hợp với yêu cầu báo cáo hàng tuần cho BOD.

### 2. Thảo luận về quy trình làm việc (Workflow) và các tính năng (Functions)
* **Quy trình làm việc chung được đề xuất bao gồm các bước chính:**
    1.  User login (tạm thời sẽ mock).
    2.  Định nghĩa constraints/goals.
    3.  Fetch existing skill:
        * Thu thập/tổng hợp kỹ năng của người dùng từ các hệ thống bên ngoài (như AkaJob, Jira, Confluence).
        * Việc này là tự động, người dùng sẽ không nhập skill trực tiếp vào hệ thống này.
        * Có kế hoạch để người dùng tự gửi lên skill của mình nhưng sẽ ưu tiên tính năng này sau.
        * Sau này có thể có background sync định kỳ để lấy thay đổi về skill của người dùng.
    4.  Generate target profile:
        * Xác định profile mục tiêu.
        * Nguồn data để định nghĩa các tiêu chí cho profile mục tiêu có thể lấy từ tài liệu nội bộ của Fsoft (ví dụ: file Excel mô tả yêu cầu theo từng level/role).
    5.  Gap analysis: Phân tích sự khác biệt (gap) giữa kỹ năng hiện có và profile mục tiêu.
    6.  Generate learning roadmap: Tạo lộ trình học tập dựa trên kết quả gap analysis.
    7.  Final report/Agent report: Tổng hợp kết quả và báo cáo cuối cùng.
* **Thảo luận về hai chế độ làm việc:**
    * **Simple mode:** Đi thẳng từ input đến kết quả cuối cùng.
    * **Advisor mode:** Đi từng bước, cho phép người dùng can thiệp và xác nhận ở mỗi bước.
    * **Đồng ý:** Nên làm Advisor mode trước, vì Simple mode có thể coi là Advisor mode chạy tự động không cần xác nhận.
* **Liệt kê các tính năng dự kiến theo từng object:**
    * **Learning request:** create/update/view
    * **Skill profile:** fetch/sync/schedule
    * **Target:** (Không rõ chi tiết)
    * **Gap:** (Không rõ chi tiết)
    * **Learning roadmap:** generate/update/export
    * **Agent report:** generate/view/export
    * Nhấn mạnh tính năng **versioning (lịch sử chat)** và khả năng **update/chỉnh sửa roadmap** bởi người dùng.
    * Tính năng **notify skill change** (thông báo khi skill thay đổi) là optional.
    * Danh sách này là đầy đủ (100%), đội sẽ chọn 50% để làm trong giai đoạn 3 tuần.

### 3. Nguồn dữ liệu (Data Sources, dự kiến sau này, còn hiện tại sẽ mockup toàn bộ data)
* **Existing skills:** Từ các hệ thống nội bộ khác của Fsoft.
* **Target profile definition:** Từ các tài liệu định nghĩa tiêu chí của Appsop.
* **Learning paths/Courses:**
    * Thảo luận về việc lấy từ các nguồn bên ngoài (Udemy, Coursera) hoặc nội bộ (Level Up).
    * Kế hoạch là ưu tiên xử lý data nội bộ trước và mock data từ các nguồn bên ngoài.
    * Sau này có thể mở rộng web search và indexing thêm.
    * Dữ liệu khóa học sẽ được phân loại bằng label để AI gợi ý.
* Dữ liệu thu thập được sẽ được nhồi vào **vector DB** để query (sử dụng RAG).

### 4. Kế hoạch chi tiết 3 tuần (Detail Plan)
* Kế hoạch chia thành **3 sprint**, mỗi sprint 1 tuần, kết thúc vào ngày **13 tháng 6**.
* Ước tính công sức khoảng **32 giờ/tuần/người**, trung bình khoảng **4.5 giờ/ngày** (chưa tính ảnh hưởng của công cụ AI).
* **Sprint 1:**
    * Tập trung vào setup (đã xong), UI khung (giao diện chat), API cơ bản.
    * Tính năng AI đầu tiên: skill sync engine và skill profile viewer.
    * **Mục tiêu:** Có giao diện chat và hiển thị skill hiện có.
* **Sprint 2:**
    * Tập trung vào luồng nghiệp vụ chính: gap analysis, target profile, gap result, và roadmap generator/display.
    * **Mục tiêu:** Hoàn thành các bước core của flow.
* **Sprint 3:**
    * Tập trung vào report (generate, view, export PDF) và các tính năng còn lại.

### 5. Thảo luận về giao diện người dùng (UI)
* Review một số mockup UI ban đầu.
* **Giao diện chính dự kiến là chat-based:**
    * Người dùng tương tác qua khung chat.
    * Hệ thống trả lời và hiển thị thông tin (skill, gap, roadmap) dưới dạng text hoặc widget.
    * Có thể bổ sung các prompts template để tiện cho người dùng.
* Bên cạnh khung chat sẽ có **lịch sử chat (history/trade)** và chức năng **export**.
* Cũng có gợi ý về giao diện truyền thống (form điền thông tin) nhưng xu hướng hiện tại thiên về chat-based.
* **Technical stack cho Frontend dự kiến:** React/Typescript.

### 6. Các vấn đề kỹ thuật và phân công sơ bộ
* **Backend:** có thể chọn TypeScript.
* **Phần AI:** làm bằng Python.
* Dữ liệu skill và course sẽ được nhồi vào **Vector DB**.
* **Phân công:**
    * **Đại:** Focus vào UI và backend.
    * **Anh Quyết:** Xem xét phần Agent/AI kiến trúc và implement.
    * **Anh Phong:** Focus vào phần RAG.
* Sẽ dùng model nội bộ hoặc model free trước.
* Có ngân sách khoảng **200$** để test với Open AI nhưng có thể dùng vào tuần thứ 2 hoặc 3.
* **Cấu trúc code repo:** Sẽ có các folder riêng cho UI, Backend, AI.
* Cần refine requirement và chia nhỏ task hơn nữa để rõ dependency.

---

Buổi họp kết thúc do hết thời gian, hẹn gặp lại vào **chiều thứ bảy** để tiếp tục.

**Lưu ý:**
* Deadline review requirement và plan hiện tại và có ý kiến chỉnh sửa hoàn thiện sẽ làm hết trong **hôm nay**.
* Sau đó vẫn có thể sửa đổi nhưng **không sửa đổi lớn** nữa.
* Cần anh em review và cho ý kiến góp ý về requirement và plan.
# Scope của sản phẩm
- <PERSON><PERSON><PERSON> khóa học tập trung vào domain nào?

# QA
## 1. Nguồn data lấy từ đâu?
- Trong nội bộ Fsoft
- Internet
## 2. Scope của ứng ứng dụng
- Tập trung vào thiết kế lộ trình học dựa trên tài nguyên có sẵn từ nhiều nguồn:
    - Quản lý đào tạo (iMocha)
    - Levelup AkaJob
    - OKR
    - Jira đánh giá năng lực
    - Coursera
    - có thể sẽ hỗ trợ nhiều nguồn trong tương lai
- Thành phần:
    - Agent: lõi của sản phẩm.
    - Web UI / App: giao diện người dùng để tương tác với agent.
## 3. Ai sẽ là đối tượng sử dụng
- Người học
    - Nâng cao kỹ năng
- Tầng lớp quản lý
    - <PERSON><PERSON> thể xem được người phù hợp cho các dự án tiếp theo
    - <PERSON><PERSON><PERSON> gi<PERSON> được năng lực của nhân viên
## 4. Fsoft hưởng lợi gì
## 5. Các tính năng của Web UI / App
- User:
    - SSO với Fsoft.
    - Đưa các input vào:
        - Đưa constraint + mục tiêu (nhâp dạng free text).
        - Không cần đưa skill hiện tại và năng lực hiện tại. (hệ thống tự thu thập)
    - View để xem gợi ý lộ trình theo input đầu vào:
        - Gap
        - Lộ trình học tập
        - Tài liệu học tập
    - Có thể export ra file hoặc expose api để integrate với các hệ thống khác.
## 6. Các tính năng của Agent / Core business
- 1. Thu thập thông tin kỹ năng + learning constraint (học được bao nhiêu giờ một tuần) hiện tại của người dùng tự động tổng hợp từ các nguồn dữ liệu có sẵn.
- 2. Thu thập mục tiêu + constraint học tập (người dùng nhập vào)
- 3. Xác định gap giữa kỹ năng hiện tại và mục tiêu
    - 3.1. Nếu thiếu thông tin thì có thể đặt thêm câu hỏi để người dùng nhập vào cho đủ thông tin.
- 4. Gợi ý lộ trình học tập phù hợp dể đạt được mục tiêu
- 5. Gợi ý nội dung học tập phù hợp với lộ trình
- 6. (Ưu tiên sau) Tracking kết quả học theo lộ trình
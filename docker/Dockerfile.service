# Multi-stage build for minimal image size
FROM python:3.12.3-slim AS builder

# Install build dependencies including C++ compiler for google-crc32c
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    build-essential \
    libffi-dev \
    libssl-dev \
    libopenblas-dev \
    liblapack-dev \
    gfortran \
    python3-dev \
    pkg-config \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Install uv for faster dependency resolution
RUN pip install --no-cache-dir uv

# Copy dependency files
COPY pyproject.toml .
COPY uv.lock .

# Install all dependencies using uv with the lock file for consistency
# This avoids dependency resolution issues and uses exact versions
RUN uv sync --frozen

# List installed packages for debugging
RUN /app/.venv/bin/pip list | grep -E "(numexpr|numpy)"

# Try to reinstall google-crc32c with C extension to avoid the warning
RUN /app/.venv/bin/pip install --force-reinstall --no-binary=google-crc32c google-crc32c

# Activate the virtual environment and install additional packages within it
RUN /app/.venv/bin/pip install --no-cache-dir --no-compile langgraph_prebuilt

# Verify critical dependencies are installed
RUN /app/.venv/bin/python -c "import numexpr; print('numexpr version:', numexpr.__version__)"
RUN /app/.venv/bin/python -c "import numpy; print('numpy version:', numpy.__version__)"

# Clean up pip cache and temporary files
RUN pip cache purge && \
    rm -rf /tmp/* /var/tmp/* /root/.cache

# Runtime stage - minimal slim image
FROM python:3.12.3-slim AS runtime

# Install only essential runtime dependencies
RUN apt-get update && apt-get install -y \
    libffi8 \
    libssl3 \
    libopenblas0 \
    liblapack3 \
    libgfortran5 \
    ca-certificates \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy Python packages from builder (uv creates a virtual environment)
COPY --from=builder /app/.venv /app/.venv

# Copy only necessary source code
COPY src/agents/ ./agents/
COPY src/core/ ./core/
COPY src/memory/ ./memory/
COPY src/schema/ ./schema/
COPY src/service/ ./service/
COPY src/run_service.py .

# Set environment variables for optimization
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONPATH=/app
ENV PYTHONOPTIMIZE=2
ENV PATH="/app/.venv/bin:$PATH"

# Clean up Python cache and optimize
RUN find . -type d -name __pycache__ -exec rm -rf {} + 2>/dev/null || true && \
    find . -name "*.pyc" -delete && \
    find . -name "*.pyo" -delete && \
    find /usr/local/lib/python3.12/site-packages -name "*.pyc" -delete && \
    find /usr/local/lib/python3.12/site-packages -type d -name __pycache__ -exec rm -rf {} + 2>/dev/null || true && \
    find /usr/local/lib/python3.12/site-packages -name "*.dist-info" -exec rm -rf {} + 2>/dev/null || true && \
    find /usr/local/lib/python3.12/site-packages -name "tests" -type d -exec rm -rf {} + 2>/dev/null || true && \
    find /usr/local/lib/python3.12/site-packages -name "test" -type d -exec rm -rf {} + 2>/dev/null || true

# Create non-root user for security
RUN groupadd --gid 1001 appgroup && \
    useradd --uid 1001 --gid appgroup --no-create-home --system appuser

# Set environment variables for optimization
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONPATH=/app
ENV PYTHONOPTIMIZE=2
ENV PATH="/app/.venv/bin:$PATH"

# Change ownership of app directory and virtual environment
RUN chown -R appuser:appgroup /app

USER appuser

EXPOSE 8000

# Use exec form for better signal handling
CMD ["python", "-O", "run_service.py"]
